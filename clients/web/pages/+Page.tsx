// src/pages/+Page.tsx

export default function HomePage() {
  return (
    <section>
      <h1 className="text-3xl font-bold mb-4">Welcome to Fadhili Community</h1>
      <p className="text-lg text-gray-700 mb-6">
        Connect, share, and grow together. Create or explore community posts, contribute insights,
        and stay informed.
      </p>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <FeatureCard
          title="Community Posts"
          description="Read and write posts shared by other users. Markdown supported!"
          href="/community"
        />
        <FeatureCard
          title="Blog"
          description="Official updates, guides, and product announcements."
          href="/blog"
        />
        <FeatureCard
          title="Todo App"
          description="Try the demo feature using local storage."
          href="/todo"
        />
      </div>
    </section>
  );
}

function FeatureCard({ title, description, href }: { title: string; description: string; href: string }) {
  return (
    <a
      href={href}
      className="block border p-4 rounded-lg shadow hover:shadow-md transition"
    >
      <h2 className="text-xl font-semibold mb-1">{title}</h2>
      <p className="text-gray-600 text-sm">{description}</p>
    </a>
  );
}
